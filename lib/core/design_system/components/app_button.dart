import 'package:flutter/material.dart';
import '../colors/app_colors.dart';
import '../spacing/app_spacing.dart';
import '../typography/app_text_styles.dart';

enum AppButtonType {
  primary,
  secondary,
  tertiary,
  mystic,
  ghost,
  destructive,
}

enum AppButtonSize {
  small,
  medium,
  large,
}

class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final Widget? icon;
  final Widget? suffixIcon;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.suffixIcon,
    this.padding,
    this.borderRadius,
  });

  const AppButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.suffixIcon,
    this.padding,
    this.borderRadius,
  }) : type = AppButtonType.primary;

  const AppButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.suffixIcon,
    this.padding,
    this.borderRadius,
  }) : type = AppButtonType.secondary;

  const AppButton.tertiary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.suffixIcon,
    this.padding,
    this.borderRadius,
  }) : type = AppButtonType.tertiary;

  const AppButton.mystic({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.suffixIcon,
    this.padding,
    this.borderRadius,
  }) : type = AppButtonType.mystic;

  const AppButton.ghost({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.suffixIcon,
    this.padding,
    this.borderRadius,
  }) : type = AppButtonType.ghost;

  const AppButton.destructive({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.suffixIcon,
    this.padding,
    this.borderRadius,
  }) : type = AppButtonType.destructive;

  @override
  Widget build(BuildContext context) {
    final textStyle = _getTextStyle();
    final buttonHeight = _getButtonHeight();
    final buttonPadding = _getButtonPadding();

    Widget buttonChild = _buildButtonContent(textStyle);

    if (isLoading) {
      buttonChild = _buildLoadingContent(textStyle);
    }

    Widget button = Container(
      height: buttonHeight,
      width: isFullWidth ? double.infinity : null,
      decoration: _getDecoration(),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: borderRadius ?? AppSpacing.buttonRadius,
          child: Container(
            padding: padding ?? buttonPadding,
            alignment: Alignment.center,
            child: buttonChild,
          ),
        ),
      ),
    );

    if (type == AppButtonType.mystic) {
      button = Container(
        decoration: BoxDecoration(
          gradient: AppColors.primaryGradient,
          borderRadius: borderRadius ?? AppSpacing.buttonRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowMystic,
              offset: const Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: isLoading ? null : onPressed,
            borderRadius: borderRadius ?? AppSpacing.buttonRadius,
            child: Container(
              height: buttonHeight,
              width: isFullWidth ? double.infinity : null,
              padding: padding ?? buttonPadding,
              alignment: Alignment.center,
              child: buttonChild,
            ),
          ),
        ),
      );
    }

    return button;
  }

  Widget _buildButtonContent(TextStyle textStyle) {
    final children = <Widget>[];

    if (icon != null) {
      children.add(icon!);
      children.add(AppSpacing.horizontalSpacingSM);
    }

    children.add(
      Text(
        text,
        style: textStyle,
        textAlign: TextAlign.center,
      ),
    );

    if (suffixIcon != null) {
      children.add(AppSpacing.horizontalSpacingSM);
      children.add(suffixIcon!);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  Widget _buildLoadingContent(TextStyle textStyle) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: _getLoadingSize(),
          height: _getLoadingSize(),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              textStyle.color ?? AppColors.textOnPrimary,
            ),
          ),
        ),
        AppSpacing.horizontalSpacingSM,
        Text(
          'Loading...',
          style: textStyle,
        ),
      ],
    );
  }

  ButtonStyle _getButtonStyle() {
    switch (type) {
      case AppButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.mysticPurple500,
          foregroundColor: AppColors.textOnPrimary,
          elevation: 4,
          shadowColor: AppColors.shadowMystic,
        );
      case AppButtonType.secondary:
        return OutlinedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.mysticPurple500,
          side: const BorderSide(color: AppColors.mysticPurple500, width: 2),
        );
      case AppButtonType.tertiary:
        return TextButton.styleFrom(
          backgroundColor: AppColors.cosmicDark700,
          foregroundColor: AppColors.textPrimary,
        );
      case AppButtonType.mystic:
        return ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.textOnPrimary,
          elevation: 0,
          shadowColor: Colors.transparent,
        );
      case AppButtonType.ghost:
        return TextButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.textSecondary,
        );
      case AppButtonType.destructive:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.error500,
          foregroundColor: AppColors.textOnPrimary,
          elevation: 4,
          shadowColor: AppColors.error500.withOpacity(0.3),
        );
    }
  }

  TextStyle _getTextStyle() {
    TextStyle baseStyle;

    switch (size) {
      case AppButtonSize.small:
        baseStyle = AppTextStyles.buttonSmall;
        break;
      case AppButtonSize.medium:
        baseStyle = AppTextStyles.buttonMedium;
        break;
      case AppButtonSize.large:
        baseStyle = AppTextStyles.buttonLarge;
        break;
    }

    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.mystic:
      case AppButtonType.destructive:
        return baseStyle.copyWith(color: AppColors.textOnPrimary);
      case AppButtonType.secondary:
        return baseStyle.copyWith(color: AppColors.mysticPurple500);
      case AppButtonType.tertiary:
        return baseStyle.copyWith(color: AppColors.textPrimary);
      case AppButtonType.ghost:
        return baseStyle.copyWith(color: AppColors.textSecondary);
    }
  }

  double _getButtonHeight() {
    switch (size) {
      case AppButtonSize.small:
        return AppSpacing.buttonHeightSM;
      case AppButtonSize.medium:
        return AppSpacing.buttonHeightMD;
      case AppButtonSize.large:
        return AppSpacing.buttonHeightLG;
    }
  }

  EdgeInsets _getButtonPadding() {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.xs,
        );
      case AppButtonSize.medium:
        return AppSpacing.buttonPadding;
      case AppButtonSize.large:
        return AppSpacing.buttonPaddingLarge;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case AppButtonSize.small:
        return 12;
      case AppButtonSize.medium:
        return 16;
      case AppButtonSize.large:
        return 20;
    }
  }

  Decoration? _getDecoration() {
    if (type == AppButtonType.mystic) {
      return null; // Handled separately
    }

    switch (type) {
      case AppButtonType.primary:
        return BoxDecoration(
          color: AppColors.mysticPurple500,
          borderRadius: borderRadius ?? AppSpacing.buttonRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowMystic,
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        );
      case AppButtonType.secondary:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: borderRadius ?? AppSpacing.buttonRadius,
          border: Border.all(
            color: AppColors.mysticPurple500,
            width: 2,
          ),
        );
      case AppButtonType.tertiary:
        return BoxDecoration(
          color: AppColors.cosmicDark700,
          borderRadius: borderRadius ?? AppSpacing.buttonRadius,
        );
      case AppButtonType.ghost:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: borderRadius ?? AppSpacing.buttonRadius,
        );
      case AppButtonType.destructive:
        return BoxDecoration(
          color: AppColors.error500,
          borderRadius: borderRadius ?? AppSpacing.buttonRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.error500.withOpacity(0.3),
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        );
      case AppButtonType.mystic:
        return null;
    }
  }
}