import 'package:flutter/material.dart';
import '../colors/app_colors.dart';
import '../spacing/app_spacing.dart';
import '../typography/app_text_styles.dart';

enum AppCardType {
  basic,
  elevated,
  outlined,
  mystic,
  gradient,
}

class AppCard extends StatelessWidget {
  final Widget child;
  final AppCardType type;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Gradient? gradient;
  final List<BoxShadow>? shadows;
  final Border? border;
  final VoidCallback? onTap;
  final double? width;
  final double? height;
  final bool isLoading;

  const AppCard({
    super.key,
    required this.child,
    this.type = AppCardType.basic,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.gradient,
    this.shadows,
    this.border,
    this.onTap,
    this.width,
    this.height,
    this.isLoading = false,
  });

  const AppCard.basic({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.onTap,
    this.width,
    this.height,
    this.isLoading = false,
  })  : type = AppCardType.basic,
        gradient = null,
        shadows = null,
        border = null;

  const AppCard.elevated({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.onTap,
    this.width,
    this.height,
    this.isLoading = false,
  })  : type = AppCardType.elevated,
        gradient = null,
        shadows = null,
        border = null;

  const AppCard.outlined({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.onTap,
    this.width,
    this.height,
    this.isLoading = false,
  })  : type = AppCardType.outlined,
        gradient = null,
        shadows = null,
        border = null;

  const AppCard.mystic({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.onTap,
    this.width,
    this.height,
    this.isLoading = false,
  })  : type = AppCardType.mystic,
        backgroundColor = null,
        gradient = null,
        shadows = null,
        border = null;

  const AppCard.gradient({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.gradient,
    this.onTap,
    this.width,
    this.height,
    this.isLoading = false,
  })  : type = AppCardType.gradient,
        backgroundColor = null,
        shadows = null,
        border = null;

  @override
  Widget build(BuildContext context) {
    Widget cardChild = isLoading ? _buildLoadingContent() : child;

    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: _getDecoration(),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppSpacing.cardRadius,
          child: Container(
            padding: padding ?? AppSpacing.cardPadding,
            child: cardChild,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.mysticPurple500),
      ),
    );
  }

  Decoration _getDecoration() {
    switch (type) {
      case AppCardType.basic:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.cosmicDark700,
          borderRadius: borderRadius ?? AppSpacing.cardRadius,
          border: border,
        );

      case AppCardType.elevated:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.cosmicDark700,
          borderRadius: borderRadius ?? AppSpacing.cardRadius,
          border: border,
          boxShadow: shadows ?? [
            const BoxShadow(
              color: AppColors.shadowPrimary,
              offset: Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
            const BoxShadow(
              color: AppColors.shadowSecondary,
              offset: Offset(0, 2),
              blurRadius: 6,
              spreadRadius: 0,
            ),
          ],
        );

      case AppCardType.outlined:
        return BoxDecoration(
          color: backgroundColor ?? Colors.transparent,
          borderRadius: borderRadius ?? AppSpacing.cardRadius,
          border: border ?? Border.all(
            color: AppColors.borderPrimary,
            width: 1,
          ),
        );

      case AppCardType.mystic:
        return BoxDecoration(
          gradient: AppColors.cardGradient,
          borderRadius: borderRadius ?? AppSpacing.cardRadius,
          border: Border.all(
            color: AppColors.mysticPurple500.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowMystic,
              offset: const Offset(0, 8),
              blurRadius: 24,
              spreadRadius: 0,
            ),
            const BoxShadow(
              color: AppColors.shadowPrimary,
              offset: Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        );

      case AppCardType.gradient:
        return BoxDecoration(
          gradient: gradient ?? AppColors.primaryGradient,
          borderRadius: borderRadius ?? AppSpacing.cardRadius,
          border: border,
          boxShadow: shadows ?? [
            BoxShadow(
              color: AppColors.shadowMystic,
              offset: const Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        );
    }
  }
}

class MysticalCard extends StatelessWidget {
  final Widget child;
  final String? title;
  final String? subtitle;
  final Widget? icon;
  final Color? accentColor;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final bool shimmerEffect;

  const MysticalCard({
    super.key,
    required this.child,
    this.title,
    this.subtitle,
    this.icon,
    this.accentColor,
    this.onTap,
    this.padding,
    this.shimmerEffect = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard.mystic(
      onTap: onTap,
      padding: padding ?? AppSpacing.cardPaddingLarge,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null || subtitle != null || icon != null)
            _buildHeader(),
          if (title != null || subtitle != null || icon != null)
            AppSpacing.verticalSpacingMD,
          child,
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        if (icon != null) ...[
          Container(
            padding: const EdgeInsets.all(AppSpacing.xs),
            decoration: BoxDecoration(
              color: accentColor?.withOpacity(0.2) ?? 
                     AppColors.mysticPurple500.withOpacity(0.2),
              borderRadius: AppSpacing.borderRadiusSM,
            ),
            child: icon!,
          ),
          AppSpacing.horizontalSpacingMD,
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (title != null)
                Text(
                  title!,
                  style: AppTextStyles.titleMedium.copyWith(
                    color: accentColor ?? AppColors.cosmicGold400,
                  ),
                ),
              if (subtitle != null) ...[
                AppSpacing.verticalSpacingXXS,
                Text(
                  subtitle!,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}

class PredictionCard extends StatelessWidget {
  final String title;
  final String prediction;
  final String? subtitle;
  final Widget? icon;
  final Color? accentColor;
  final VoidCallback? onTap;
  final List<String>? tags;

  const PredictionCard({
    super.key,
    required this.title,
    required this.prediction,
    this.subtitle,
    this.icon,
    this.accentColor,
    this.onTap,
    this.tags,
  });

  @override
  Widget build(BuildContext context) {
    return MysticalCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      accentColor: accentColor,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            prediction,
            style: AppTextStyles.bodyLarge,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          if (tags != null && tags!.isNotEmpty) ...[
            AppSpacing.verticalSpacingMD,
            Wrap(
              spacing: AppSpacing.xs,
              runSpacing: AppSpacing.xs,
              children: tags!.map((tag) => _buildTag(tag)).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xxs,
      ),
      decoration: BoxDecoration(
        color: accentColor?.withOpacity(0.2) ?? 
               AppColors.mysticPurple500.withOpacity(0.2),
        borderRadius: AppSpacing.borderRadiusXS,
        border: Border.all(
          color: accentColor?.withOpacity(0.5) ?? 
                 AppColors.mysticPurple500.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Text(
        tag,
        style: AppTextStyles.labelSmall.copyWith(
          color: accentColor ?? AppColors.cosmicGold400,
        ),
      ),
    );
  }
}