import 'package:flutter/material.dart';
import '../colors/app_colors.dart';
import '../spacing/app_spacing.dart';
import '../typography/app_text_styles.dart';
import 'app_card.dart';

class TarotCard extends StatelessWidget {
  final String cardName;
  final String? cardDescription;
  final String? imageUrl;
  final bool isReversed;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool showGlow;

  const TarotCard({
    super.key,
    required this.cardName,
    this.cardDescription,
    this.imageUrl,
    this.isReversed = false,
    this.onTap,
    this.isSelected = false,
    this.showGlow = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: AppSpacing.tarotCardWidth,
        height: AppSpacing.tarotCardHeight,
        decoration: BoxDecoration(
          gradient: isSelected ? AppColors.primaryGradient : AppColors.cardGradient,
          borderRadius: AppSpacing.tarotCardRadius,
          border: Border.all(
            color: isSelected 
                ? AppColors.cosmicGold400 
                : AppColors.mysticPurple500.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            if (showGlow)
              BoxShadow(
                color: AppColors.cosmicGold400.withOpacity(0.5),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            BoxShadow(
              color: AppColors.shadowMystic,
              offset: const Offset(0, 8),
              blurRadius: 16,
            ),
          ],
        ),
        child: Transform(
          alignment: Alignment.center,
          transform: isReversed ? Matrix4.rotationZ(3.14159) : Matrix4.identity(),
          child: ClipRRect(
            borderRadius: AppSpacing.tarotCardRadius,
            child: Stack(
              children: [
                // Background pattern
                Container(
                  decoration: const BoxDecoration(
                    gradient: AppColors.mysticGradient,
                  ),
                ),
                
                // Card image placeholder
                if (imageUrl != null)
                  Positioned.fill(
                    child: Image.network(
                      imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
                    ),
                  )
                else
                  _buildPlaceholder(),
                
                // Card title
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: AppSpacing.paddingSM,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Colors.transparent, Colors.black87],
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          cardName,
                          style: AppTextStyles.titleSmall.copyWith(
                            color: AppColors.cosmicGold400,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (cardDescription != null) ...[
                          AppSpacing.verticalSpacingXXS,
                          Text(
                            cardDescription!,
                            style: AppTextStyles.captionSmall,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                
                // Reversed indicator
                if (isReversed)
                  Positioned(
                    top: AppSpacing.xs,
                    right: AppSpacing.xs,
                    child: Container(
                      padding: const EdgeInsets.all(AppSpacing.xxs),
                      decoration: BoxDecoration(
                        color: AppColors.mysticalRose400,
                        borderRadius: AppSpacing.borderRadiusXS,
                      ),
                      child: const Icon(
                        Icons.flip,
                        size: AppSpacing.iconSM,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.mysticGradient,
      ),
      child: const Center(
        child: Icon(
          Icons.auto_awesome,
          size: AppSpacing.iconHuge,
          color: AppColors.cosmicGold400,
        ),
      ),
    );
  }
}

class SystemCard extends StatelessWidget {
  final String systemName;
  final String description;
  final IconData icon;
  final Color accentColor;
  final VoidCallback? onTap;
  final bool isAvailable;
  final bool isPremium;

  const SystemCard({
    super.key,
    required this.systemName,
    required this.description,
    required this.icon,
    required this.accentColor,
    this.onTap,
    this.isAvailable = true,
    this.isPremium = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard.mystic(
      onTap: isAvailable ? onTap : null,
      child: SizedBox(
        height: AppSpacing.systemCardHeight,
        child: Stack(
          children: [
            // Content
            Padding(
              padding: AppSpacing.paddingMD,
              child: Row(
                children: [
                  // Icon container
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [accentColor, accentColor.withOpacity(0.7)],
                      ),
                      borderRadius: AppSpacing.borderRadiusMD,
                      boxShadow: [
                        BoxShadow(
                          color: accentColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: AppSpacing.iconLG,
                    ),
                  ),
                  
                  AppSpacing.horizontalSpacingMD,
                  
                  // Text content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          systemName,
                          style: AppTextStyles.titleMedium.copyWith(
                            color: accentColor,
                          ),
                        ),
                        AppSpacing.verticalSpacingXXS,
                        Text(
                          description,
                          style: AppTextStyles.bodySmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  // Arrow or lock icon
                  Icon(
                    isAvailable ? Icons.arrow_forward_ios : Icons.lock,
                    color: isAvailable ? accentColor : AppColors.textDisabled,
                    size: AppSpacing.iconSM,
                  ),
                ],
              ),
            ),
            
            // Premium badge
            if (isPremium)
              Positioned(
                top: AppSpacing.xs,
                right: AppSpacing.xs,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: AppSpacing.xxs,
                  ),
                  decoration: BoxDecoration(
                    gradient: AppColors.secondaryGradient,
                    borderRadius: AppSpacing.borderRadiusXS,
                  ),
                  child: Text(
                    'PREMIUM',
                    style: AppTextStyles.labelSmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            
            // Disabled overlay
            if (!isAvailable)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.overlay,
                    borderRadius: AppSpacing.cardRadius,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class RuneSymbol extends StatelessWidget {
  final String runeName;
  final String runeSymbol;
  final String? meaning;
  final VoidCallback? onTap;
  final bool isSelected;

  const RuneSymbol({
    super.key,
    required this.runeName,
    required this.runeSymbol,
    this.meaning,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: AppSpacing.paddingMD,
        decoration: BoxDecoration(
          gradient: isSelected ? AppColors.primaryGradient : null,
          color: isSelected ? null : AppColors.cosmicDark700,
          borderRadius: AppSpacing.borderRadiusMD,
          border: Border.all(
            color: isSelected ? AppColors.cosmicGold400 : AppColors.borderPrimary,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.shadowMystic,
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              runeSymbol,
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColors.cosmicGold400,
              ),
            ),
            AppSpacing.verticalSpacingXS,
            Text(
              runeName,
              style: AppTextStyles.labelMedium.copyWith(
                color: isSelected ? Colors.white : AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            if (meaning != null) ...[
              AppSpacing.verticalSpacingXXS,
              Text(
                meaning!,
                style: AppTextStyles.captionSmall,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class CrystalCard extends StatelessWidget {
  final String crystalName;
  final String properties;
  final Color crystalColor;
  final VoidCallback? onTap;

  const CrystalCard({
    super.key,
    required this.crystalName,
    required this.properties,
    required this.crystalColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard.mystic(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: crystalColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: crystalColor.withOpacity(0.5),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.diamond,
                  color: Colors.white,
                  size: AppSpacing.iconMD,
                ),
              ),
              AppSpacing.horizontalSpacingMD,
              Expanded(
                child: Text(
                  crystalName,
                  style: AppTextStyles.titleMedium.copyWith(
                    color: crystalColor,
                  ),
                ),
              ),
            ],
          ),
          AppSpacing.verticalSpacingMD,
          Text(
            properties,
            style: AppTextStyles.bodyMedium,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

class NumerologyNumber extends StatelessWidget {
  final int number;
  final String meaning;
  final String? description;
  final bool isLucky;
  final VoidCallback? onTap;

  const NumerologyNumber({
    super.key,
    required this.number,
    required this.meaning,
    this.description,
    this.isLucky = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: AppSpacing.paddingMD,
        decoration: BoxDecoration(
          gradient: isLucky ? AppColors.secondaryGradient : null,
          color: isLucky ? null : AppColors.cosmicDark700,
          borderRadius: AppSpacing.borderRadiusMD,
          border: Border.all(
            color: isLucky ? AppColors.cosmicGold400 : AppColors.borderPrimary,
            width: isLucky ? 2 : 1,
          ),
          boxShadow: isLucky
              ? [
                  BoxShadow(
                    color: AppColors.shadowGold,
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isLucky ? Colors.white : AppColors.mysticPurple500,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  number.toString(),
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: isLucky ? AppColors.cosmicGold600 : Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            AppSpacing.verticalSpacingSM,
            Text(
              meaning,
              style: AppTextStyles.titleSmall.copyWith(
                color: isLucky ? Colors.white : AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            if (description != null) ...[
              AppSpacing.verticalSpacingXS,
              Text(
                description!,
                style: AppTextStyles.bodySmall.copyWith(
                  color: isLucky ? Colors.white70 : AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class AstrologySign extends StatelessWidget {
  final String signName;
  final String element;
  final IconData signIcon;
  final Color elementColor;
  final VoidCallback? onTap;
  final bool isActive;

  const AstrologySign({
    super.key,
    required this.signName,
    required this.element,
    required this.signIcon,
    required this.elementColor,
    this.onTap,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: AppSpacing.paddingMD,
        decoration: BoxDecoration(
          gradient: isActive 
              ? LinearGradient(
                  colors: [elementColor, elementColor.withOpacity(0.7)],
                )
              : null,
          color: isActive ? null : AppColors.cosmicDark700,
          borderRadius: AppSpacing.borderRadiusMD,
          border: Border.all(
            color: isActive ? elementColor : AppColors.borderPrimary,
            width: isActive ? 2 : 1,
          ),
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: elementColor.withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              signIcon,
              size: AppSpacing.iconXL,
              color: isActive ? Colors.white : elementColor,
            ),
            AppSpacing.verticalSpacingSM,
            Text(
              signName,
              style: AppTextStyles.titleSmall.copyWith(
                color: isActive ? Colors.white : AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            AppSpacing.verticalSpacingXXS,
            Text(
              element,
              style: AppTextStyles.captionMedium.copyWith(
                color: isActive ? Colors.white70 : elementColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}