import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../errors/exceptions.dart';
import '../models/ai_response_models.dart';
import '../../features/predictions/domain/entities/prediction.dart';

abstract class AIClient {
  Future<AIPredictionResponse> generatePrediction(String prompt);
  Future<AISummaryResponse> generateSummary(String prompt);
  Future<String> enhanceContent(String prompt);
}

class OpenAIClient implements AIClient {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _model = 'gpt-4-turbo-preview';
  
  final http.Client _httpClient;

  OpenAIClient({http.Client? httpClient}) 
      : _httpClient = httpClient ?? http.Client();

  @override
  Future<AIPredictionResponse> generatePrediction(String prompt) async {
    try {
      final response = await _makeRequest(
        '/chat/completions',
        {
          'model': _model,
          'messages': [
            {
              'role': 'system',
              'content': _getSystemPrompt(),
            },
            {
              'role': 'user',
              'content': prompt,
            }
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
          'response_format': {'type': 'json_object'},
        },
      );

      final content = response['choices'][0]['message']['content'] as String;
      final jsonData = jsonDecode(content) as Map<String, dynamic>;
      
      return _parsePredictionResponse(jsonData);
    } catch (e) {
      throw ServerException(message: 'Failed to generate prediction: ${e.toString()}');
    }
  }

  @override
  Future<AISummaryResponse> generateSummary(String prompt) async {
    try {
      final response = await _makeRequest(
        '/chat/completions',
        {
          'model': _model,
          'messages': [
            {
              'role': 'system',
              'content': _getSummarySystemPrompt(),
            },
            {
              'role': 'user',
              'content': prompt,
            }
          ],
          'max_tokens': 500,
          'temperature': 0.6,
          'response_format': {'type': 'json_object'},
        },
      );

      final content = response['choices'][0]['message']['content'] as String;
      final jsonData = jsonDecode(content) as Map<String, dynamic>;
      
      return _parseSummaryResponse(jsonData);
    } catch (e) {
      throw ServerException(message: 'Failed to generate summary: ${e.toString()}');
    }
  }

  @override
  Future<String> enhanceContent(String prompt) async {
    try {
      final response = await _makeRequest(
        '/chat/completions',
        {
          'model': _model,
          'messages': [
            {
              'role': 'system',
              'content': 'You are an expert mystical content writer. Enhance the given content to be more engaging, insightful, and mystical while maintaining accuracy.',
            },
            {
              'role': 'user',
              'content': prompt,
            }
          ],
          'max_tokens': 800,
          'temperature': 0.8,
        },
      );

      return response['choices'][0]['message']['content'] as String;
    } catch (e) {
      throw ServerException(message: 'Failed to enhance content: ${e.toString()}');
    }
  }

  Future<Map<String, dynamic>> _makeRequest(String endpoint, Map<String, dynamic> body) async {
    if (!AppConfig.hasOpenAI) {
      throw ServerException(message: 'OpenAI API key not configured');
    }

    final response = await _httpClient.post(
      Uri.parse('$_baseUrl$endpoint'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${AppConfig.openaiApiKey}',
        if (AppConfig.openaiOrganizationId.isNotEmpty)
          'OpenAI-Organization': AppConfig.openaiOrganizationId,
      },
      body: jsonEncode(body),
    );

    if (response.statusCode != 200) {
      final errorData = jsonDecode(response.body);
      throw ServerException(
        message: 'OpenAI API error: ${errorData['error']['message']}',
      );
    }

    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  String _getSystemPrompt() {
    return '''
You are a professional mystical advisor with expertise in astrology, numerology, Chinese astrology, and Ayurveda. 
Generate personalized predictions that are insightful, practical, and accurate to the mystical traditions.

IMPORTANT: You must respond in valid JSON format with the following structure:
{
  "title": "string (max 60 characters)",
  "content": "string (150-250 words)",
  "summary": "string (max 120 characters)",
  "keywords": ["string", "string", "string"] (3-5 keywords),
  "advice": "string (max 100 words)",
  "warning": "string (max 80 words)",
  "lucky_numbers": ["string", "string", "string"] (3-5 numbers),
  "lucky_colors": ["string", "string"] (2-3 colors),
  "energy_level": number (1-10),
  "mood": "string" (excellent/good/neutral/challenging/difficult)
}

Keep predictions positive but realistic. Focus on actionable guidance.
''';
  }

  String _getSummarySystemPrompt() {
    return '''
You are a mystical advisor creating daily summaries from multiple prediction types.
Synthesize the information into a cohesive overview.

IMPORTANT: You must respond in valid JSON format with the following structure:
{
  "overall_mood": "string" (excellent/good/neutral/challenging/difficult),
  "overall_energy_level": number (1-10),
  "daily_theme": "string" (max 50 characters),
  "top_keywords": ["string", "string", "string", "string", "string"] (5-7 keywords),
  "main_advice": "string" (max 150 words),
  "primary_focus": "string" (max 80 characters),
  "avoid_actions": ["string", "string", "string"] (3-5 actions),
  "favorable_actions": ["string", "string", "string"] (3-5 actions)
}

Create a unified perspective that highlights common themes and guidance.
''';
  }

  AIPredictionResponse _parsePredictionResponse(Map<String, dynamic> json) {
    try {
      return AIPredictionResponse(
        title: json['title'] as String,
        content: json['content'] as String,
        summary: json['summary'] as String,
        keywords: List<String>.from(json['keywords'] ?? []),
        mood: _parseMood(json['mood'] as String),
        energyLevel: json['energy_level'] as int,
        luckyNumbers: List<String>.from(json['lucky_numbers'] ?? []),
        luckyColors: List<String>.from(json['lucky_colors'] ?? []),
        advice: json['advice'] as String,
        warning: json['warning'] as String,
      );
    } catch (e) {
      throw ServerException(message: 'Failed to parse prediction response: ${e.toString()}');
    }
  }

  AISummaryResponse _parseSummaryResponse(Map<String, dynamic> json) {
    try {
      return AISummaryResponse(
        overallMood: _parseMood(json['overall_mood'] as String),
        overallEnergyLevel: json['overall_energy_level'] as int,
        dailyTheme: json['daily_theme'] as String,
        topKeywords: List<String>.from(json['top_keywords'] ?? []),
        mainAdvice: json['main_advice'] as String,
        primaryFocus: json['primary_focus'] as String,
        avoidActions: List<String>.from(json['avoid_actions'] ?? []),
        favorableActions: List<String>.from(json['favorable_actions'] ?? []),
      );
    } catch (e) {
      throw ServerException(message: 'Failed to parse summary response: ${e.toString()}');
    }
  }

  PredictionMood _parseMood(String moodString) {
    switch (moodString.toLowerCase()) {
      case 'excellent':
        return PredictionMood.excellent;
      case 'good':
        return PredictionMood.good;
      case 'challenging':
        return PredictionMood.challenging;
      case 'difficult':
        return PredictionMood.difficult;
      default:
        return PredictionMood.neutral;
    }
  }
}

// Fallback client for when no AI is available
class MockAIClient implements AIClient {
  @override
  Future<AIPredictionResponse> generatePrediction(String prompt) async {
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 2));
    
    return AIPredictionResponse(
      title: 'Mock Prediction Title',
      content: 'This is a mock prediction generated when no AI API is available. In production, this would contain real mystical insights based on your personal data and current cosmic influences.',
      summary: 'Mock prediction for testing purposes',
      keywords: ['mock', 'testing', 'placeholder'],
      mood: PredictionMood.neutral,
      energyLevel: 5,
      luckyNumbers: ['7', '22', '33'],
      luckyColors: ['Blue', 'Silver'],
      advice: 'This is mock advice for testing the application without real AI integration.',
      warning: 'This is a mock warning for testing purposes.',
    );
  }

  @override
  Future<AISummaryResponse> generateSummary(String prompt) async {
    await Future.delayed(const Duration(seconds: 1));
    
    return AISummaryResponse(
      overallMood: PredictionMood.neutral,
      overallEnergyLevel: 5,
      dailyTheme: 'Mock Testing Day',
      topKeywords: ['mock', 'testing', 'development', 'placeholder'],
      mainAdvice: 'This is mock daily advice for testing the application.',
      primaryFocus: 'Testing and Development',
      avoidActions: ['Worry about mock data', 'Skip testing'],
      favorableActions: ['Test thoroughly', 'Review mock responses'],
    );
  }

  @override
  Future<String> enhanceContent(String prompt) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return 'Enhanced: $prompt';
  }
}