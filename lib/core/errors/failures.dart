import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  const Failure();
  
  @override
  List<Object> get props => [];
}

// General failures
class ServerFailure extends Failure {
  final String message;
  
  const ServerFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class CacheFailure extends Failure {
  final String message;
  
  const CacheFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class NetworkFailure extends Failure {
  final String message;
  
  const NetworkFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class AuthenticationFailure extends Failure {
  final String message;
  
  const AuthenticationFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class SubscriptionFailure extends Failure {
  final String message;
  
  const SubscriptionFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class LocationFailure extends Failure {
  final String message;
  
  const LocationFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class WeatherFailure extends Failure {
  final String message;
  
  const WeatherFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class AstrologyFailure extends Failure {
  final String message;
  
  const AstrologyFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class TarotFailure extends Failure {
  final String message;
  
  const TarotFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class NumerologyFailure extends Failure {
  final String message;
  
  const NumerologyFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class ValidationFailure extends Failure {
  final String message;
  
  const ValidationFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}

class UnknownFailure extends Failure {
  final String message;
  
  const UnknownFailure({required this.message});
  
  @override
  List<Object> get props => [message];
}