import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/design_system/design_system.dart';

class OnboardingPage extends ConsumerWidget {
  const OnboardingPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Padding(
            padding: AppSpacing.screenPadding,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(),
                
                // Logo/Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: AppColors.primaryGradient,
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                
                AppSpacing.verticalSpacingHuge,
                
                // Title
                Text(
                  'Premium Oracle',
                  style: AppTextStyles.mysticTitle,
                  textAlign: TextAlign.center,
                ),
                
                AppSpacing.verticalSpacingMD,
                
                // Subtitle
                Text(
                  'Complete Mystical Consultation in Your Pocket',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                AppSpacing.verticalSpacingXS,
                
                Text(
                  '15+ Mystical Systems • AI Powered • Personalized',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.cosmicGold400,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const Spacer(),
                
                // Features
                _buildFeatureItem(
                  context,
                  Icons.stars,
                  'Daily Personalized Predictions',
                ),
                const SizedBox(height: 16),
                _buildFeatureItem(
                  context,
                  Icons.psychology,
                  'AI-Powered Mystical Insights',
                ),
                const SizedBox(height: 16),
                _buildFeatureItem(
                  context,
                  Icons.auto_graph,
                  'Track Your Spiritual Journey',
                ),
                
                const Spacer(),
                
                // Get Started Button
                AppButton.mystic(
                  text: 'Get Started',
                  onPressed: () => context.go('/register'),
                  isFullWidth: true,
                  size: AppButtonSize.large,
                ),
                
                AppSpacing.verticalSpacingMD,
                
                // Login Button  
                AppButton.secondary(
                  text: 'I Already Have an Account',
                  onPressed: () => context.go('/login'),
                  isFullWidth: true,
                  size: AppButtonSize.large,
                ),
                
                AppSpacing.verticalSpacingLG,
                
                // Design System Demo Button (for development)
                AppButton.ghost(
                  text: 'View Design System',
                  onPressed: () => context.go('/design-system'),
                  isFullWidth: true,
                ),
                
                AppSpacing.verticalSpacingXL,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, IconData icon, String text) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.mysticPurple500.withOpacity(0.2),
            borderRadius: AppSpacing.borderRadiusSM,
          ),
          child: Icon(
            icon,
            color: AppColors.cosmicGold400,
            size: AppSpacing.iconMD,
          ),
        ),
        AppSpacing.horizontalSpacingMD,
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }
}