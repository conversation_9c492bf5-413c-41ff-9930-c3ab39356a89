import 'package:flutter/material.dart';
import '../../../../core/design_system/design_system.dart';
import '../../domain/entities/birth_data.dart' as entities;

class BirthTimePicker extends StatelessWidget {
  final entities.TimeOfDay? initialTime;
  final Function(entities.TimeOfDay) onTimeSelected;

  const BirthTimePicker({
    super.key,
    this.initialTime,
    required this.onTimeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => _showTimePicker(context),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: AppColors.primary500,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    initialTime != null
                        ? initialTime!.toDisplayString()
                        : 'Select birth time (optional)',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: initialTime != null ? Colors.white : Colors.white60,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white60,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        
        // Quick time options
        Text(
          'Or select approximate time:',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickTimeChip(context, 'Early Morning', const entities.TimeOfDay(hour: 6, minute: 0)),
            _buildQuickTimeChip(context, 'Morning', const entities.TimeOfDay(hour: 9, minute: 0)),
            _buildQuickTimeChip(context, 'Midday', const entities.TimeOfDay(hour: 12, minute: 0)),
            _buildQuickTimeChip(context, 'Afternoon', const entities.TimeOfDay(hour: 15, minute: 0)),
            _buildQuickTimeChip(context, 'Evening', const entities.TimeOfDay(hour: 18, minute: 0)),
            _buildQuickTimeChip(context, 'Night', const entities.TimeOfDay(hour: 21, minute: 0)),
            _buildQuickTimeChip(context, 'Late Night', const entities.TimeOfDay(hour: 0, minute: 0)),
          ],
        ),
        
        if (initialTime == null) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.warning500.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.warning500.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.warning500,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Birth time affects rising sign and house positions in your astrological chart',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.warning500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildQuickTimeChip(BuildContext context, String label, entities.TimeOfDay time) {
    final isSelected = initialTime?.hour == time.hour && initialTime?.minute == time.minute;
    
    return GestureDetector(
      onTap: () => onTimeSelected(time),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.primary500.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected 
                ? AppColors.primary500
                : Colors.white.withOpacity(0.2),
          ),
        ),
        child: Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: isSelected ? AppColors.primary300 : Colors.white70,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Future<void> _showTimePicker(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: initialTime != null 
          ? TimeOfDay(hour: initialTime!.hour, minute: initialTime!.minute)
          : const TimeOfDay(hour: 12, minute: 0),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: AppColors.primary500,
              onPrimary: Colors.white,
              surface: AppColors.dark800,
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      onTimeSelected(entities.TimeOfDay(hour: picked.hour, minute: picked.minute));
    }
  }
}