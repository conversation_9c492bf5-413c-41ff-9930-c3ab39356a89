import 'package:flutter/material.dart';
import '../../../../core/design_system/design_system.dart';
import '../../domain/entities/birth_data.dart';

class BirthLocationPicker extends StatefulWidget {
  final BirthLocation? initialLocation;
  final Function(BirthLocation) onLocationSelected;

  const BirthLocationPicker({
    super.key,
    this.initialLocation,
    required this.onLocationSelected,
  });

  @override
  State<BirthLocationPicker> createState() => _BirthLocationPickerState();
}

class _BirthLocationPickerState extends State<BirthLocationPicker> {
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _countryController = TextEditingController();
  final _searchController = TextEditingController();
  
  bool _isSearchMode = false;
  bool _isManualMode = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation != null) {
      _cityController.text = widget.initialLocation!.cityName;
      _stateController.text = widget.initialLocation!.stateName ?? '';
      _countryController.text = widget.initialLocation!.countryName;
      _isManualMode = true;
    }
  }

  @override
  void dispose() {
    _cityController.dispose();
    _stateController.dispose();
    _countryController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.initialLocation != null && !_isManualMode)
          _buildSelectedLocationDisplay(),
        
        if (!_isManualMode && widget.initialLocation == null)
          _buildLocationOptions(),
        
        if (_isSearchMode)
          _buildLocationSearch(),
        
        if (_isManualMode)
          _buildManualLocationEntry(),
      ],
    );
  }

  Widget _buildSelectedLocationDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.success500.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.success500.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on,
            color: AppColors.success500,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.initialLocation!.fullLocationName,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  widget.initialLocation!.coordinatesString,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _isManualMode = true;
              });
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationOptions() {
    return Column(
      children: [
        // Search option
        GestureDetector(
          onTap: () {
            setState(() {
              _isSearchMode = true;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.search,
                  color: AppColors.primary500,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Search for your birth city',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white60,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Manual entry option
        GestureDetector(
          onTap: () {
            setState(() {
              _isManualMode = true;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.edit_location,
                  color: AppColors.primary500,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Enter location manually',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white60,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Info about location importance
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.info500.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.info500.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.info500,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Location affects astrological house calculations and local mean time',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.info500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLocationSearch() {
    return Column(
      children: [
        // Back button
        Row(
          children: [
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _isSearchMode = false;
                  _searchController.clear();
                });
              },
              icon: const Icon(Icons.arrow_back, size: 16),
              label: const Text('Back'),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Search field
        TextFormField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Search for city or location...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            // TODO: Implement location search
          },
        ),
        
        const SizedBox(height: 16),
        
        // Search results would go here
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'Location search functionality will be implemented with Google Places API',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white70,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildManualLocationEntry() {
    return Column(
      children: [
        // Back button if not initial
        if (widget.initialLocation == null)
          Row(
            children: [
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _isManualMode = false;
                    _cityController.clear();
                    _stateController.clear();
                    _countryController.clear();
                  });
                },
                icon: const Icon(Icons.arrow_back, size: 16),
                label: const Text('Back'),
              ),
            ],
          ),
        
        const SizedBox(height: 8),
        
        // City field
        TextFormField(
          controller: _cityController,
          decoration: const InputDecoration(
            labelText: 'City *',
            hintText: 'Enter birth city',
            prefixIcon: Icon(Icons.location_city),
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'City is required';
            }
            return null;
          },
          onChanged: (value) => _updateLocation(),
        ),
        
        const SizedBox(height: 16),
        
        // State/Province field
        TextFormField(
          controller: _stateController,
          decoration: const InputDecoration(
            labelText: 'State/Province',
            hintText: 'Enter state or province (optional)',
            prefixIcon: Icon(Icons.map),
          ),
          onChanged: (value) => _updateLocation(),
        ),
        
        const SizedBox(height: 16),
        
        // Country field
        TextFormField(
          controller: _countryController,
          decoration: const InputDecoration(
            labelText: 'Country *',
            hintText: 'Enter country',
            prefixIcon: Icon(Icons.public),
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Country is required';
            }
            return null;
          },
          onChanged: (value) => _updateLocation(),
        ),
        
        const SizedBox(height: 16),
        
        // Note about coordinates
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.warning500.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.warning500.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.warning_amber_outlined,
                color: AppColors.warning500,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Coordinates will be estimated. For more accurate readings, use location search.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.warning500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _updateLocation() {
    if (_cityController.text.isNotEmpty && _countryController.text.isNotEmpty) {
      // Create a basic location with estimated coordinates
      // In a real app, you would geocode the location
      final location = BirthLocation(
        cityName: _cityController.text.trim(),
        stateName: _stateController.text.trim().isEmpty ? null : _stateController.text.trim(),
        countryName: _countryController.text.trim(),
        latitude: 0.0, // Would be geocoded
        longitude: 0.0, // Would be geocoded
        timezone: null, // Would be determined from coordinates
      );
      
      widget.onLocationSelected(location);
    }
  }
}