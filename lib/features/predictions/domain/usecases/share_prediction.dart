import '../repositories/prediction_repository.dart';

class SharePrediction {
  final PredictionRepository repository;

  SharePrediction(this.repository);

  Future<void> call({
    required String predictionId,
    required String shareMethod,
  }) async {
    return await repository.sharePrediction(
      predictionId: predictionId,
      shareMethod: shareMethod,
    );
  }

  Future<void> shareViaText(String predictionId) async {
    return call(predictionId: predictionId, shareMethod: 'text');
  }

  Future<void> shareViaEmail(String predictionId) async {
    return call(predictionId: predictionId, shareMethod: 'email');
  }

  Future<void> shareViaTwitter(String predictionId) async {
    return call(predictionId: predictionId, shareMethod: 'twitter');
  }

  Future<void> shareViaInstagram(String predictionId) async {
    return call(predictionId: predictionId, shareMethod: 'instagram');
  }

  Future<void> shareViaFacebook(String predictionId) async {
    return call(predictionId: predictionId, shareMethod: 'facebook');
  }
}