import '../entities/prediction.dart';
import '../repositories/prediction_repository.dart';

class GetPredictionHistory {
  final PredictionRepository repository;

  GetPredictionHistory(this.repository);

  Future<List<PredictionCollection>> call({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await repository.getPredictionHistory(
      userId: userId,
      startDate: startDate,
      endDate: endDate,
    );
  }

  Future<List<PredictionCollection>> getLastWeek(String userId) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 7));
    
    return call(
      userId: userId,
      startDate: startDate,
      endDate: endDate,
    );
  }

  Future<List<PredictionCollection>> getLastMonth(String userId) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 30));
    
    return call(
      userId: userId,
      startDate: startDate,
      endDate: endDate,
    );
  }
}