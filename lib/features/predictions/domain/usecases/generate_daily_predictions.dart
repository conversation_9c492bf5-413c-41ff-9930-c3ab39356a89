import '../entities/prediction.dart';
import '../repositories/prediction_repository.dart';

class GenerateDailyPredictions {
  final PredictionRepository repository;

  GenerateDailyPredictions(this.repository);

  Future<PredictionCollection> call({
    required String userId,
    required DateTime date,
  }) async {
    return await repository.generateDailyPredictions(
      userId: userId,
      date: date,
    );
  }
}