import '../entities/prediction.dart';
import '../repositories/prediction_repository.dart';

class GetDailyPredictions {
  final PredictionRepository repository;

  GetDailyPredictions(this.repository);

  Future<PredictionCollection?> call({
    required String userId,
    required DateTime date,
  }) async {
    return await repository.getDailyPredictions(
      userId: userId,
      date: date,
    );
  }
}