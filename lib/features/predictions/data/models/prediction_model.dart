import '../../domain/entities/prediction.dart';

class DailyPredictionModel extends DailyPrediction {
  const DailyPredictionModel({
    required String id,
    required String userId,
    required DateTime date,
    required PredictionType type,
    required String title,
    required String content,
    required String summary,
    required List<String> keywords,
    required PredictionMood mood,
    required int energyLevel,
    required List<String> luckyNumbers,
    required List<String> luckyColors,
    required String advice,
    required String warning,
    required Map<String, dynamic> metadata,
    required DateTime createdAt,
    DateTime? viewedAt,
    bool isShared = false,
  }) : super(
          id: id,
          userId: userId,
          date: date,
          type: type,
          title: title,
          content: content,
          summary: summary,
          keywords: keywords,
          mood: mood,
          energyLevel: energyLevel,
          luckyNumbers: luckyNumbers,
          luckyColors: luckyColors,
          advice: advice,
          warning: warning,
          metadata: metadata,
          createdAt: createdAt,
          viewedAt: viewedAt,
          isShared: isShared,
        );

  factory DailyPredictionModel.fromJson(Map<String, dynamic> json) {
    return DailyPredictionModel(
      id: json['id'],
      userId: json['user_id'],
      date: DateTime.parse(json['date']),
      type: PredictionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      title: json['title'],
      content: json['content'],
      summary: json['summary'],
      keywords: List<String>.from(json['keywords'] ?? []),
      mood: PredictionMood.values.firstWhere(
        (e) => e.toString().split('.').last == json['mood'],
      ),
      energyLevel: json['energy_level'],
      luckyNumbers: List<String>.from(json['lucky_numbers'] ?? []),
      luckyColors: List<String>.from(json['lucky_colors'] ?? []),
      advice: json['advice'],
      warning: json['warning'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      createdAt: DateTime.parse(json['created_at']),
      viewedAt: json['viewed_at'] != null ? DateTime.parse(json['viewed_at']) : null,
      isShared: json['is_shared'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'date': '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}',
      'type': type.toString().split('.').last,
      'title': title,
      'content': content,
      'summary': summary,
      'keywords': keywords,
      'mood': mood.toString().split('.').last,
      'energy_level': energyLevel,
      'lucky_numbers': luckyNumbers,
      'lucky_colors': luckyColors,
      'advice': advice,
      'warning': warning,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'viewed_at': viewedAt?.toIso8601String(),
      'is_shared': isShared,
    };
  }

  factory DailyPredictionModel.fromEntity(DailyPrediction entity) {
    return DailyPredictionModel(
      id: entity.id,
      userId: entity.userId,
      date: entity.date,
      type: entity.type,
      title: entity.title,
      content: entity.content,
      summary: entity.summary,
      keywords: entity.keywords,
      mood: entity.mood,
      energyLevel: entity.energyLevel,
      luckyNumbers: entity.luckyNumbers,
      luckyColors: entity.luckyColors,
      advice: entity.advice,
      warning: entity.warning,
      metadata: entity.metadata,
      createdAt: entity.createdAt,
      viewedAt: entity.viewedAt,
      isShared: entity.isShared,
    );
  }

  DailyPrediction toEntity() {
    return DailyPrediction(
      id: id,
      userId: userId,
      date: date,
      type: type,
      title: title,
      content: content,
      summary: summary,
      keywords: keywords,
      mood: mood,
      energyLevel: energyLevel,
      luckyNumbers: luckyNumbers,
      luckyColors: luckyColors,
      advice: advice,
      warning: warning,
      metadata: metadata,
      createdAt: createdAt,
      viewedAt: viewedAt,
      isShared: isShared,
    );
  }
}

class PredictionCollectionModel extends PredictionCollection {
  const PredictionCollectionModel({
    required String userId,
    required DateTime date,
    required List<DailyPrediction> predictions,
    required PredictionSummary summary,
    required DateTime createdAt,
  }) : super(
          userId: userId,
          date: date,
          predictions: predictions,
          summary: summary,
          createdAt: createdAt,
        );

  factory PredictionCollectionModel.fromJson(Map<String, dynamic> json) {
    final predictionsData = json['predictions'] as List<dynamic>? ?? [];
    final predictions = predictionsData
        .map((p) => DailyPredictionModel.fromJson(p))
        .toList();

    return PredictionCollectionModel(
      userId: json['user_id'],
      date: DateTime.parse(json['date']),
      predictions: predictions,
      summary: PredictionSummaryModel.fromJson(json['summary'] ?? {}),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'date': '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}',
      'predictions': predictions
          .map((p) => DailyPredictionModel.fromEntity(p).toJson())
          .toList(),
      'summary': PredictionSummaryModel.fromEntity(summary).toJson(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory PredictionCollectionModel.fromEntity(PredictionCollection entity) {
    return PredictionCollectionModel(
      userId: entity.userId,
      date: entity.date,
      predictions: entity.predictions,
      summary: entity.summary,
      createdAt: entity.createdAt,
    );
  }

  PredictionCollection toEntity() {
    return PredictionCollection(
      userId: userId,
      date: date,
      predictions: predictions,
      summary: summary,
      createdAt: createdAt,
    );
  }
}

class PredictionSummaryModel extends PredictionSummary {
  const PredictionSummaryModel({
    required PredictionMood overallMood,
    required int overallEnergyLevel,
    required String dailyTheme,
    required List<String> topKeywords,
    required String mainAdvice,
    required String primaryFocus,
    required List<String> avoidActions,
    required List<String> favorableActions,
  }) : super(
          overallMood: overallMood,
          overallEnergyLevel: overallEnergyLevel,
          dailyTheme: dailyTheme,
          topKeywords: topKeywords,
          mainAdvice: mainAdvice,
          primaryFocus: primaryFocus,
          avoidActions: avoidActions,
          favorableActions: favorableActions,
        );

  factory PredictionSummaryModel.fromJson(Map<String, dynamic> json) {
    // Handle empty or null JSON
    if (json.isEmpty) {
      return const PredictionSummaryModel(
        overallMood: PredictionMood.neutral,
        overallEnergyLevel: 5,
        dailyTheme: 'Unknown',
        topKeywords: [],
        mainAdvice: 'No advice available',
        primaryFocus: 'Unknown',
        avoidActions: [],
        favorableActions: [],
      );
    }

    return PredictionSummaryModel(
      overallMood: PredictionMood.values.firstWhere(
        (e) => e.toString().split('.').last == json['overall_mood'],
        orElse: () => PredictionMood.neutral,
      ),
      overallEnergyLevel: json['overall_energy_level'] ?? 5,
      dailyTheme: json['daily_theme'] ?? 'Unknown',
      topKeywords: List<String>.from(json['top_keywords'] ?? []),
      mainAdvice: json['main_advice'] ?? 'No advice available',
      primaryFocus: json['primary_focus'] ?? 'Unknown',
      avoidActions: List<String>.from(json['avoid_actions'] ?? []),
      favorableActions: List<String>.from(json['favorable_actions'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overall_mood': overallMood.toString().split('.').last,
      'overall_energy_level': overallEnergyLevel,
      'daily_theme': dailyTheme,
      'top_keywords': topKeywords,
      'main_advice': mainAdvice,
      'primary_focus': primaryFocus,
      'avoid_actions': avoidActions,
      'favorable_actions': favorableActions,
    };
  }

  factory PredictionSummaryModel.fromEntity(PredictionSummary entity) {
    return PredictionSummaryModel(
      overallMood: entity.overallMood,
      overallEnergyLevel: entity.overallEnergyLevel,
      dailyTheme: entity.dailyTheme,
      topKeywords: entity.topKeywords,
      mainAdvice: entity.mainAdvice,
      primaryFocus: entity.primaryFocus,
      avoidActions: entity.avoidActions,
      favorableActions: entity.favorableActions,
    );
  }

  PredictionSummary toEntity() {
    return PredictionSummary(
      overallMood: overallMood,
      overallEnergyLevel: overallEnergyLevel,
      dailyTheme: dailyTheme,
      topKeywords: topKeywords,
      mainAdvice: mainAdvice,
      primaryFocus: primaryFocus,
      avoidActions: avoidActions,
      favorableActions: favorableActions,
    );
  }
}