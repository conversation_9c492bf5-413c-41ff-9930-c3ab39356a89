import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../../user_profile/domain/repositories/user_profile_repository.dart';
import '../../domain/entities/prediction.dart';
import '../../domain/repositories/prediction_repository.dart';
import '../../domain/services/ai_prediction_service.dart';
import '../../domain/services/prediction_context_service.dart';
import '../datasources/prediction_remote_datasource.dart';
import '../models/prediction_model.dart';

class PredictionRepositoryImpl implements PredictionRepository {
  final PredictionRemoteDataSource remoteDataSource;
  final UserProfileRepository userProfileRepository;
  final PredictionContextService contextService;
  final AIPredictionService aiService;
  final NetworkInfo networkInfo;
  final PredictionCacheService cacheService;

  PredictionRepositoryImpl({
    required this.remoteDataSource,
    required this.userProfileRepository,
    required this.contextService,
    required this.aiService,
    required this.networkInfo,
    required this.cacheService,
  });

  @override
  Future<PredictionCollection> generateDailyPredictions({
    required String userId,
    required DateTime date,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      // Check if predictions already exist
      final existing = await getDailyPredictions(userId: userId, date: date);
      if (existing != null && existing.isComplete) {
        return existing;
      }

      // Get user profile
      final userProfile = await userProfileRepository.getUserProfile(userId);
      if (userProfile == null) {
        throw ServerException(message: 'User profile not found');
      }

      // Calculate prediction context
      final context = await contextService.calculatePredictionContext(
        userId: userId,
        date: date,
        userProfile: userProfile,
      );

      // Generate individual predictions
      final predictions = <DailyPrediction>[];

      // Generate astrological prediction
      final astrologicalPrediction = await aiService.generateAstrologicalPrediction(
        context: context,
        astrologicalData: context.astrologicalContext,
      );
      predictions.add(astrologicalPrediction);

      // Generate numerological prediction
      final numerologicalPrediction = await aiService.generateNumerologicalPrediction(
        context: context,
        numerologicalData: context.numerologicalContext,
      );
      predictions.add(numerologicalPrediction);

      // Generate Chinese prediction
      final chinesePrediction = await aiService.generateChinesePrediction(
        context: context,
        chineseData: context.chineseContext,
      );
      predictions.add(chinesePrediction);

      // Generate Ayurvedic prediction
      final ayurvedicPrediction = await aiService.generateAyurvedicPrediction(
        context: context,
        ayurvedicData: context.ayurvedicContext,
      );
      predictions.add(ayurvedicPrediction);

      // Generate daily summary
      final summary = await aiService.generateDailySummary(
        predictions: predictions,
        context: context,
      );

      // Create collection
      final collection = PredictionCollection(
        userId: userId,
        date: date,
        predictions: predictions,
        summary: summary,
        createdAt: DateTime.now(),
      );

      // Save to database and cache
      final savedCollection = await saveDailyPredictions(collection);
      await cacheService.cachePredictions(savedCollection);

      return savedCollection;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to generate daily predictions: ${e.toString()}');
    }
  }

  @override
  Future<PredictionCollection?> getDailyPredictions({
    required String userId,
    required DateTime date,
  }) async {
    try {
      // Try cache first
      final cached = await cacheService.getCachedPredictions(userId, date);
      if (cached != null) {
        return cached;
      }

      // Fallback to database
      if (await networkInfo.isConnected) {
        final collectionModel = await remoteDataSource.getDailyPredictions(
          userId: userId,
          date: date,
        );
        
        if (collectionModel != null) {
          final collection = collectionModel.toEntity();
          await cacheService.cachePredictions(collection);
          return collection;
        }
      }

      return null;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get daily predictions: ${e.toString()}');
    }
  }

  @override
  Future<PredictionCollection> saveDailyPredictions(PredictionCollection collection) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final collectionModel = PredictionCollectionModel.fromEntity(collection);
      final savedModel = await remoteDataSource.saveDailyPredictions(collectionModel);
      return savedModel.toEntity();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to save daily predictions: ${e.toString()}');
    }
  }

  @override
  Future<DailyPrediction?> getPredictionByType({
    required String userId,
    required DateTime date,
    required PredictionType type,
  }) async {
    try {
      // Try to get from daily predictions first
      final collection = await getDailyPredictions(userId: userId, date: date);
      if (collection != null) {
        return collection.getPredictionByType(type);
      }

      // Fallback to individual prediction lookup
      if (await networkInfo.isConnected) {
        final predictionModel = await remoteDataSource.getPredictionByType(
          userId: userId,
          date: date,
          type: type.toString().split('.').last,
        );
        return predictionModel?.toEntity();
      }

      return null;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get prediction by type: ${e.toString()}');
    }
  }

  @override
  Future<List<PredictionCollection>> getPredictionHistory({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final collectionModels = await remoteDataSource.getPredictionHistory(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      return collectionModels.map((model) => model.toEntity()).toList();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get prediction history: ${e.toString()}');
    }
  }

  @override
  Future<List<DailyPrediction>> getUserPredictionsByType({
    required String userId,
    required PredictionType type,
    required int limit,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final predictionModels = await remoteDataSource.getUserPredictionsByType(
        userId: userId,
        type: type.toString().split('.').last,
        limit: limit,
      );

      return predictionModels.map((model) => model.toEntity()).toList();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get user predictions by type: ${e.toString()}');
    }
  }

  @override
  Future<void> markPredictionAsViewed({
    required String predictionId,
    required String userId,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await remoteDataSource.markPredictionAsViewed(
        predictionId: predictionId,
        userId: userId,
      );

      // Update cache if prediction is cached
      await cacheService.updatePredictionViewStatus(predictionId, userId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to mark prediction as viewed: ${e.toString()}');
    }
  }

  @override
  Future<void> sharePrediction({
    required String predictionId,
    required String shareMethod,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await remoteDataSource.sharePrediction(
        predictionId: predictionId,
        shareMethod: shareMethod,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to share prediction: ${e.toString()}');
    }
  }

  @override
  Future<void> deletePrediction(String predictionId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await remoteDataSource.deletePrediction(predictionId);
      await cacheService.removePredictionFromCache(predictionId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to delete prediction: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteUserPredictions(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await remoteDataSource.deleteUserPredictions(userId);
      await cacheService.clearUserCache(userId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to delete user predictions: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getPredictionAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      return await remoteDataSource.getPredictionAnalytics(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get prediction analytics: ${e.toString()}');
    }
  }

  @override
  Future<List<String>> getMostAccuratePredictionTypes(String userId) async {
    try {
      final analytics = await getPredictionAnalytics(
        userId: userId,
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now(),
      );

      final typeDistribution = analytics['type_distribution'] as Map<String, dynamic>? ?? {};
      final types = typeDistribution.entries.toList()
        ..sort((a, b) => (b.value as int).compareTo(a.value as int));

      return types.map((e) => e.key).toList();
    } catch (e) {
      throw ServerException(message: 'Failed to get most accurate prediction types: ${e.toString()}');
    }
  }

  @override
  Future<Map<PredictionMood, int>> getMoodDistribution({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final analytics = await getPredictionAnalytics(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      final moodDistribution = analytics['mood_distribution'] as Map<String, dynamic>? ?? {};
      final result = <PredictionMood, int>{};

      for (final entry in moodDistribution.entries) {
        final mood = PredictionMood.values.firstWhere(
          (m) => m.toString().split('.').last == entry.key,
          orElse: () => PredictionMood.neutral,
        );
        result[mood] = entry.value as int;
      }

      return result;
    } catch (e) {
      throw ServerException(message: 'Failed to get mood distribution: ${e.toString()}');
    }
  }

  @override
  Future<void> preloadPredictions({
    required String userId,
    required List<DateTime> dates,
  }) async {
    try {
      for (final date in dates) {
        // Check if already cached
        final cached = await cacheService.getCachedPredictions(userId, date);
        if (cached == null) {
          // Try to load from database
          final predictions = await getDailyPredictions(userId: userId, date: date);
          if (predictions != null) {
            await cacheService.cachePredictions(predictions);
          }
        }
      }
    } catch (e) {
      throw ServerException(message: 'Failed to preload predictions: ${e.toString()}');
    }
  }

  @override
  Future<bool> arePredictionsAvailable({
    required String userId,
    required DateTime date,
  }) async {
    try {
      // Check cache first
      final cached = await cacheService.getCachedPredictions(userId, date);
      if (cached != null) return true;

      // Check database
      if (await networkInfo.isConnected) {
        return await remoteDataSource.arePredictionsAvailable(
          userId: userId,
          date: date,
        );
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> clearPredictionCache(String userId) async {
    try {
      await cacheService.clearUserCache(userId);
    } catch (e) {
      throw ServerException(message: 'Failed to clear prediction cache: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> exportPredictions(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      return await remoteDataSource.exportPredictions(userId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to export predictions: ${e.toString()}');
    }
  }

  @override
  Future<void> importPredictions({
    required String userId,
    required Map<String, dynamic> data,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await remoteDataSource.importPredictions(userId: userId, data: data);
      await cacheService.clearUserCache(userId); // Clear cache to force reload
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to import predictions: ${e.toString()}');
    }
  }
}

// Supporting cache service
abstract class PredictionCacheService {
  Future<PredictionCollection?> getCachedPredictions(String userId, DateTime date);
  Future<void> cachePredictions(PredictionCollection collection);
  Future<void> updatePredictionViewStatus(String predictionId, String userId);
  Future<void> removePredictionFromCache(String predictionId);
  Future<void> clearUserCache(String userId);
  Future<void> clearAllCache();
}

class PredictionCacheServiceImpl implements PredictionCacheService {
  final Map<String, PredictionCollection> _cache = {};
  static const int maxCacheSize = 100;
  static const Duration cacheExpiry = Duration(hours: 24);

  @override
  Future<PredictionCollection?> getCachedPredictions(String userId, DateTime date) async {
    final key = _generateCacheKey(userId, date);
    final cached = _cache[key];
    
    if (cached != null) {
      // Check if cache is still valid
      final cacheAge = DateTime.now().difference(cached.createdAt);
      if (cacheAge <= cacheExpiry) {
        return cached;
      } else {
        _cache.remove(key);
      }
    }
    
    return null;
  }

  @override
  Future<void> cachePredictions(PredictionCollection collection) async {
    final key = _generateCacheKey(collection.userId, collection.date);
    
    // Implement LRU cache eviction
    if (_cache.length >= maxCacheSize) {
      final oldestKey = _cache.keys.first;
      _cache.remove(oldestKey);
    }
    
    _cache[key] = collection;
  }

  @override
  Future<void> updatePredictionViewStatus(String predictionId, String userId) async {
    // Update all cached collections that contain this prediction
    for (final collection in _cache.values) {
      if (collection.userId == userId) {
        final predictionIndex = collection.predictions.indexWhere((p) => p.id == predictionId);
        if (predictionIndex != -1) {
          final updatedPrediction = collection.predictions[predictionIndex].copyWith(
            viewedAt: DateTime.now(),
          );
          final updatedPredictions = List<DailyPrediction>.from(collection.predictions);
          updatedPredictions[predictionIndex] = updatedPrediction;
          
          final updatedCollection = PredictionCollection(
            userId: collection.userId,
            date: collection.date,
            predictions: updatedPredictions,
            summary: collection.summary,
            createdAt: collection.createdAt,
          );
          
          final key = _generateCacheKey(collection.userId, collection.date);
          _cache[key] = updatedCollection;
        }
      }
    }
  }

  @override
  Future<void> removePredictionFromCache(String predictionId) async {
    final keysToUpdate = <String>[];
    
    for (final entry in _cache.entries) {
      final collection = entry.value;
      if (collection.predictions.any((p) => p.id == predictionId)) {
        keysToUpdate.add(entry.key);
      }
    }
    
    for (final key in keysToUpdate) {
      _cache.remove(key);
    }
  }

  @override
  Future<void> clearUserCache(String userId) async {
    final keysToRemove = _cache.entries
        .where((entry) => entry.value.userId == userId)
        .map((entry) => entry.key)
        .toList();
    
    for (final key in keysToRemove) {
      _cache.remove(key);
    }
  }

  @override
  Future<void> clearAllCache() async {
    _cache.clear();
  }

  String _generateCacheKey(String userId, DateTime date) {
    return '${userId}_${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}