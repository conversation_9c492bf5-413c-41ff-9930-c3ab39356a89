import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/prediction.dart';
import '../../domain/usecases/generate_daily_predictions.dart';
import '../../domain/usecases/get_daily_predictions.dart';
import '../../domain/usecases/mark_prediction_viewed.dart';

// State class for predictions
class PredictionState {
  final bool isLoading;
  final PredictionCollection? currentCollection;
  final List<PredictionCollection> history;
  final String? error;

  const PredictionState({
    this.isLoading = false,
    this.currentCollection,
    this.history = const [],
    this.error,
  });

  PredictionState copyWith({
    bool? isLoading,
    PredictionCollection? currentCollection,
    List<PredictionCollection>? history,
    String? error,
  }) {
    return PredictionState(
      isLoading: isLoading ?? this.isLoading,
      currentCollection: currentCollection ?? this.currentCollection,
      history: history ?? this.history,
      error: error,
    );
  }
}

// Notifier class for predictions
class PredictionNotifier extends StateNotifier<PredictionState> {
  final GetDailyPredictions getDailyPredictions;
  final GenerateDailyPredictions generateDailyPredictions;
  final MarkPredictionViewed markPredictionViewed;

  PredictionNotifier({
    required this.getDailyPredictions,
    required this.generateDailyPredictions,
    required this.markPredictionViewed,
  }) : super(const PredictionState());

  Future<void> loadDailyPredictions(DateTime date) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // First try to get existing predictions
      var predictions = await getDailyPredictions(
        userId: 'current_user_id', // TODO: Get from auth provider
        date: date,
      );

      // If no predictions exist, generate them
      if (predictions == null) {
        predictions = await generateDailyPredictions(
          userId: 'current_user_id', // TODO: Get from auth provider
          date: date,
        );
      }

      state = state.copyWith(
        isLoading: false,
        currentCollection: predictions,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> markAsViewed(String predictionId) async {
    try {
      await markPredictionViewed(
        predictionId: predictionId,
        userId: 'current_user_id', // TODO: Get from auth provider
      );
    } catch (e) {
      // Handle error silently for view tracking
      print('Error marking prediction as viewed: $e');
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider for the prediction notifier
final predictionNotifierProvider = StateNotifierProvider<PredictionNotifier, PredictionState>((ref) {
  // TODO: Inject actual use cases when dependency injection is set up
  throw UnimplementedError('Prediction dependencies not yet configured');
});

// TODO: These providers will be implemented when we set up dependency injection
// final getDailyPredictionsProvider = Provider<GetDailyPredictions>((ref) {
//   return GetDailyPredictions(ref.read(predictionRepositoryProvider));
// });

// final generateDailyPredictionsProvider = Provider<GenerateDailyPredictions>((ref) {
//   return GenerateDailyPredictions(ref.read(predictionRepositoryProvider));
// });

// final markPredictionViewedProvider = Provider<MarkPredictionViewed>((ref) {
//   return MarkPredictionViewed(ref.read(predictionRepositoryProvider));
// });