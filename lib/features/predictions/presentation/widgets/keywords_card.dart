import 'package:flutter/material.dart';
import '../../../../core/design_system/design_system.dart';

class KeywordsCard extends StatelessWidget {
  final List<String> keywords;
  final String title;
  
  const KeywordsCard({
    super.key,
    required this.keywords,
    this.title = 'Key Influences',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: keywords.map((keyword) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary500.withOpacity(0.3),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                keyword,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primary300,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }
}
