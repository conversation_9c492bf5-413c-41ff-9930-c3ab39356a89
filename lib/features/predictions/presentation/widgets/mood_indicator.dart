import 'package:flutter/material.dart';
import '../../domain/entities/prediction.dart';

class MoodIndicator extends StatelessWidget {
  final PredictionMood mood;
  final double size;
  final bool showLabel;

  const MoodIndicator({
    super.key,
    required this.mood,
    this.size = 40,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(size * 0.2),
      decoration: BoxDecoration(
        color: Color(int.parse('0xFF${mood.color.substring(1)}'))
            .withOpacity(0.2),
        borderRadius: BorderRadius.circular(size * 0.2),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            mood.emoji,
            style: TextStyle(fontSize: size * 0.5),
          ),
          if (showLabel) ...[
            SizedBox(height: size * 0.1),
            Text(
              mood.displayName,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color(int.parse('0xFF${mood.color.substring(1)}')),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }
}