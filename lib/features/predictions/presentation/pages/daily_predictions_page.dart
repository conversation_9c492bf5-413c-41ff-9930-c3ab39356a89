import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/design_system.dart' hide PredictionCard;
import '../../domain/entities/prediction.dart';
import '../widgets/widgets.dart';
import '../providers/prediction_notifier.dart';

class DailyPredictionsPage extends ConsumerStatefulWidget {
  const DailyPredictionsPage({super.key});

  @override
  ConsumerState<DailyPredictionsPage> createState() => _DailyPredictionsPageState();
}

class _DailyPredictionsPageState extends ConsumerState<DailyPredictionsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _pageController = PageController();
    
    // Load predictions for today
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPredictions();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _loadPredictions() {
    ref.read(predictionNotifierProvider.notifier).loadDailyPredictions(_selectedDate);
  }

  @override
  Widget build(BuildContext context) {
    final predictionState = ref.watch(predictionNotifierProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header with date picker
              _buildHeader(context),
              
              // Summary card or loading
              if (predictionState.isLoading)
                const PredictionLoadingShimmer()
              else if (predictionState.currentCollection != null)
                PredictionSummaryCard(
                  summary: predictionState.currentCollection!.summary,
                  date: _selectedDate,
                )
              else
                EmptyStateWidget(onButtonPressed: _loadPredictions,isExpanded: false),

              const SizedBox(height: 16),

              // Tab bar for prediction types
              if (predictionState.currentCollection != null)
                _buildTabBar(),

              // Prediction cards
              Expanded(
                child: _buildPredictionContent(predictionState),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(predictionState),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: AppSpacing.screenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Greeting and date
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getGreeting(),
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(_selectedDate),
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
              
              // Date picker button
              IconButton(
                onPressed: _showDatePicker,
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.calendar_today,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary500,
          borderRadius: BorderRadius.circular(25),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white60,
        labelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.normal,
        ),
        onTap: (index) {
          _pageController.animateToPage(
            index,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
        tabs: const [
          Tab(text: 'Summary'),
          Tab(text: '⭐'),
          Tab(text: '🔢'),
          Tab(text: '🐲'),
          Tab(text: '🧘'),
        ],
      ),
    );
  }

  Widget _buildPredictionContent(PredictionState state) {
    if (state.isLoading) {
      return const PredictionLoadingShimmer();
    }

    if (state.currentCollection == null) {
      return EmptyStateWidget(onButtonPressed: _loadPredictions);
    }

    return PageView(
      controller: _pageController,
      onPageChanged: (index) {
        _tabController.animateTo(index);
      },
      children: [
        // Summary tab
        _buildSummaryTab(state.currentCollection!),
        
        // Individual prediction tabs
        ...PredictionType.values.map((type) {
          final prediction = state.currentCollection!.getPredictionByType(type);
          return prediction != null
              ? _buildPredictionTab(prediction)
              : _buildMissingPredictionTab(type);
        }),
      ],
    );
  }

  Widget _buildSummaryTab(PredictionCollection collection) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Overall mood and energy
          Row(
            children: [
              Expanded(
                child: MoodIndicator(
                  mood: collection.summary.overallMood,
                  showLabel: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: EnergyIndicator(
                  energyLevel: collection.summary.overallEnergyLevel,
                  showLabel: true,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Daily theme
          ThemeCard(theme: collection.summary.dailyTheme),
          
          const SizedBox(height: 16),
          
          // Keywords
          KeywordsCard(keywords: collection.summary.topKeywords),
          
          const SizedBox(height: 16),
          
          // Actions
          Row(
            children: [
              Expanded(
                child: ActionCard(
                  title: 'Favorable Actions',
                  actions: collection.summary.favorableActions,
                  color: AppColors.success500,
                  icon: Icons.trending_up,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ActionCard(
                  title: 'Avoid Actions',
                  actions: collection.summary.avoidActions,
                  color: AppColors.warning500,
                  icon: Icons.warning_outlined,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionTab(DailyPrediction prediction) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: PredictionCard(
        prediction: prediction,
        onViewed: () {
          ref.read(predictionNotifierProvider.notifier).markAsViewed(prediction.id);
        },
        onShare: () {
          ShareOptionsBottomSheet.show(
            context: context,
            prediction: prediction,
          );
        },
      ),
    );
  }

  Widget _buildMissingPredictionTab(PredictionType type) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Text(
                type.icon,
                style: const TextStyle(fontSize: 48),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '${type.displayName} Prediction',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Not available for this date',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white60,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadPredictions,
              child: const Text('Generate Predictions'),
            ),
          ],
        ),
      ),
    );
  }







  Widget? _buildFloatingActionButton(PredictionState state) {
    if (state.isLoading) return null;
    
    return FloatingActionButton.extended(
      onPressed: _loadPredictions,
      backgroundColor: AppColors.primary500,
      icon: const Icon(Icons.refresh),
      label: const Text('Refresh'),
    );
  }

  void _showDatePicker() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: AppColors.primary500,
              onPrimary: Colors.white,
              surface: AppColors.dark800,
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadPredictions();
    }
  }



  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  }

  String _formatDate(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    final today = DateTime.now();
    final tomorrow = today.add(const Duration(days: 1));
    final yesterday = today.subtract(const Duration(days: 1));
    
    if (_isSameDay(date, today)) return 'Today';
    if (_isSameDay(date, tomorrow)) return 'Tomorrow';
    if (_isSameDay(date, yesterday)) return 'Yesterday';
    
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}

