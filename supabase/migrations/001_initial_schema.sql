-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Birth data table
CREATE TABLE public.birth_data (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    birth_date DATE NOT NULL,
    birth_time JSONB, -- {hour: int, minute: int}
    birth_location JSONB, -- {city_name, state_name, country_name, latitude, longitude, timezone}
    has_accurate_birth_time BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Mystical profiles table
CREATE TABLE public.mystical_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    astrological_profile JSONB,
    numerological_profile JSONB,
    chinese_profile JSONB,
    ayurvedic_profile JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Prediction collections table
CREATE TABLE public.prediction_collections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    summary JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- Daily predictions table
CREATE TABLE public.daily_predictions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    collection_id UUID REFERENCES public.prediction_collections(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('astrological', 'numerological', 'chinese', 'ayurvedic')),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT NOT NULL,
    keywords TEXT[] DEFAULT '{}',
    mood TEXT NOT NULL CHECK (mood IN ('excellent', 'good', 'neutral', 'challenging', 'difficult')),
    energy_level INTEGER NOT NULL CHECK (energy_level >= 1 AND energy_level <= 10),
    lucky_numbers TEXT[] DEFAULT '{}',
    lucky_colors TEXT[] DEFAULT '{}',
    advice TEXT NOT NULL,
    warning TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    viewed_at TIMESTAMP WITH TIME ZONE,
    is_shared BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date, type)
);

-- Prediction shares table
CREATE TABLE public.prediction_shares (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    prediction_id UUID REFERENCES public.daily_predictions(id) ON DELETE CASCADE,
    share_method TEXT NOT NULL,
    shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Profile backups table
CREATE TABLE public.profile_backups (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    backup_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_birth_data_user_id ON public.birth_data(user_id);
CREATE INDEX idx_mystical_profiles_user_id ON public.mystical_profiles(user_id);
CREATE INDEX idx_prediction_collections_user_date ON public.prediction_collections(user_id, date);
CREATE INDEX idx_daily_predictions_user_date ON public.daily_predictions(user_id, date);
CREATE INDEX idx_daily_predictions_type ON public.daily_predictions(type);
CREATE INDEX idx_daily_predictions_viewed ON public.daily_predictions(viewed_at) WHERE viewed_at IS NOT NULL;

-- Row Level Security (RLS)
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.birth_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mystical_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prediction_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prediction_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profile_backups ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own birth data" ON public.birth_data
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own mystical profiles" ON public.mystical_profiles
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own prediction collections" ON public.prediction_collections
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own predictions" ON public.daily_predictions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own prediction shares" ON public.prediction_shares
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.daily_predictions 
            WHERE id = prediction_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can view own profile backups" ON public.profile_backups
    FOR ALL USING (auth.uid() = user_id);

-- Functions for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON public.user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_birth_data_updated_at 
    BEFORE UPDATE ON public.birth_data 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mystical_profiles_updated_at 
    BEFORE UPDATE ON public.mystical_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();